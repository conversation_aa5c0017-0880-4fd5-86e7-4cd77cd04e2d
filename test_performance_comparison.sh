#!/bin/bash

# 性能对比测试脚本
BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="223"

echo "=== 接口性能对比测试 ==="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo ""

# 准备测试数据
DEPT_DATA='[
  {
    "deptId": 1,
    "deptName": "内科",
    "deptCode": "NK",
    "parentId": 0,
    "deptLevel": 1,
    "status": 1
  },
  {
    "deptId": 2,
    "deptName": "外科",
    "deptCode": "WK",
    "parentId": 0,
    "deptLevel": 1,
    "status": 1
  }
]'

PATIENT_DATA='[
  {
    "inpatientInfoId": "P001",
    "name": "张三",
    "idCard": "110101199001011234",
    "mobile": "13800138001",
    "sex": 1,
    "age": 30,
    "hospitalizationNo": "H001",
    "inhospitalDiagnose": "高血压",
    "deptId": 1,
    "sickbedNo": "1-101",
    "inhospitalTime": "2024-01-01T10:00:00",
    "status": 1
  },
  {
    "inpatientInfoId": "P002",
    "name": "李四",
    "idCard": "110101199002021234",
    "mobile": "13800138002",
    "sex": 2,
    "age": 25,
    "hospitalizationNo": "H002",
    "inhospitalDiagnose": "糖尿病",
    "deptId": 2,
    "sickbedNo": "2-102",
    "inhospitalTime": "2024-01-02T11:00:00",
    "status": 1
  }
]'

echo "1. 测试全量同步接口（科室数据）"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/department"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"

START_TIME=$(date +%s%3N)
RESPONSE1=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/department" \
  -H "Content-Type: application/json" \
  -d "$DEPT_DATA")
END_TIME=$(date +%s%3N)
DURATION1=$((END_TIME - START_TIME))

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
echo "接口响应时间: ${DURATION1}ms"
echo "响应结果:"
echo "$RESPONSE1" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE1"
echo ""

# 提取任务ID并等待完成
TASK_ID1=$(echo "$RESPONSE1" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)
if [ ! -z "$TASK_ID1" ]; then
    echo "等待全量同步任务完成..."
    sleep 3
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID1" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

echo "2. 测试患者同步接口（住院患者）"
echo "请求URL: $BASE_URL/api/sync/patient/$HOSPITAL_ID/in"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"

START_TIME=$(date +%s%3N)
RESPONSE2=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/in" \
  -H "Content-Type: application/json" \
  -d "$PATIENT_DATA")
END_TIME=$(date +%s%3N)
DURATION2=$((END_TIME - START_TIME))

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
echo "接口响应时间: ${DURATION2}ms"
echo "响应结果:"
echo "$RESPONSE2" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE2"
echo ""

# 提取任务ID并等待完成
TASK_ID2=$(echo "$RESPONSE2" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)
if [ ! -z "$TASK_ID2" ]; then
    echo "等待患者同步任务完成..."
    sleep 3
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID2" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

echo "=== 性能对比结果 ==="
echo "全量同步接口响应时间: ${DURATION1}ms"
echo "患者同步接口响应时间: ${DURATION2}ms"

if [ $DURATION1 -lt $DURATION2 ]; then
    DIFF=$((DURATION2 - DURATION1))
    echo "全量同步接口比患者接口快 ${DIFF}ms"
elif [ $DURATION1 -gt $DURATION2 ]; then
    DIFF=$((DURATION1 - DURATION2))
    echo "患者同步接口比全量接口快 ${DIFF}ms"
else
    echo "两个接口响应时间相同"
fi

echo ""
echo "注意："
echo "1. 这里测试的是接口响应时间，不是任务执行时间"
echo "2. 任务执行时间需要查看日志或通过任务状态接口获取"
echo "3. 全量同步是全量替换，患者同步是增量更新"
