#!/bin/bash

# 简单的全量同步接口测试脚本
BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="223"

echo "=== 测试全量同步接口（参考患者数据同步方式） ==="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo ""

# 测试科室数据全量同步
echo "1. 测试科室数据全量同步"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/department"

DEPT_DATA='[
  {
    "deptId": 1,
    "deptName": "内科",
    "deptCode": "NK",
    "parentId": 0,
    "deptLevel": 1,
    "status": 1
  },
  {
    "deptId": 2,
    "deptName": "外科",
    "deptCode": "WK",
    "parentId": 0,
    "deptLevel": 1,
    "status": 1
  }
]'

echo "发送请求..."
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/department" \
  -H "Content-Type: application/json" \
  -d "$DEPT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待3秒后查询任务状态..."
    sleep 3
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "=== 测试完成 ==="
