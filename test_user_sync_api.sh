#!/bin/bash

# 医护人员数据同步接口测试脚本
# 使用方法: ./test_user_sync_api.sh

BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="H001"

echo "=== 医护人员数据同步接口测试 ==="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo ""

# 测试1: 医护人员数据全量同步（提供数据）
echo "1. 测试医护人员数据全量同步（提供数据）"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/user"

USER_DATA='[
  {
    "userName": "D001",
    "name": "张医生",
    "sex": 1,
    "roleId": 1,
    "deptId": 1,
    "mobile": "13800138001",
    "inpatientWard": "内科一病区",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "userName": "N001",
    "name": "李护士",
    "sex": 2,
    "roleId": 2,
    "deptId": 1,
    "mobile": "13800138002",
    "inpatientWard": "内科一病区,内科二病区",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "userName": "D002",
    "name": "王医生",
    "sex": 1,
    "roleId": 1,
    "deptId": 2,
    "mobile": "13800138003",
    "inpatientWard": "外科一病区",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "userName": "N002",
    "name": "赵护士",
    "sex": 2,
    "roleId": 2,
    "deptId": 2,
    "mobile": "13800138004",
    "inpatientWard": "外科一病区,外科二病区",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "userName": "D003",
    "name": "孙医生",
    "sex": 1,
    "roleId": 1,
    "deptId": 3,
    "mobile": "13800138005",
    "inpatientWard": "儿科病区",
    "updatedAt": "2024-07-18T10:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
START_TIME=$(date +%s%3N)

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/user" \
  -H "Content-Type: application/json" \
  -d "$USER_DATA")

END_TIME=$(date +%s%3N)
DURATION=$((END_TIME - START_TIME))

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
echo "接口响应时间: ${DURATION}ms"
echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待3秒后查询任务状态..."
    sleep 3
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

# 测试2: 医护人员数据全量同步（不提供数据，从外部系统获取）
echo "2. 测试医护人员数据全量同步（不提供数据）"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/user"

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
START_TIME=$(date +%s%3N)

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/user" \
  -H "Content-Type: application/json")

END_TIME=$(date +%s%3N)
DURATION=$((END_TIME - START_TIME))

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
echo "接口响应时间: ${DURATION}ms"
echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待5秒后查询任务状态（从外部系统获取数据可能需要更长时间）..."
    sleep 5
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

# 测试3: 查询同步状态
echo "3. 查询医护人员数据同步状态"
echo "请求URL: $BASE_URL/api/sync/status/$HOSPITAL_ID/user"

RESPONSE=$(curl -s -X GET "$BASE_URL/api/sync/status/$HOSPITAL_ID/user")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

echo "----------------------------------------"

# 测试4: 查询最后同步时间
echo "4. 查询医护人员数据最后同步时间"
echo "请求URL: $BASE_URL/api/sync/last-time/$HOSPITAL_ID/user"

RESPONSE=$(curl -s -X GET "$BASE_URL/api/sync/last-time/$HOSPITAL_ID/user")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "说明："
echo "1. 医护人员数据包含医生和护士两种角色"
echo "2. roleId: 1-医生, 2-护士"
echo "3. sex: 1-男, 2-女"
echo "4. inpatientWard 可以包含多个病区，用逗号分隔"
echo "5. userName 是唯一标识，通常是工号"
echo "6. 全量同步会完全替换Redis中的原有数据"
echo "7. 可以通过任务ID查询具体的处理状态和结果"
