#!/bin/bash

# 测试患者数据auto分类的全量同步接口
BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="223"

echo "=== 测试患者数据auto分类全量同步 ==="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo ""

# 准备测试患者数据（包含不同状态的患者）
PATIENT_DATA='[
  {
    "inpatientInfoId": "P001",
    "name": "张三",
    "idCard": "110101199001011234",
    "mobile": "13800138001",
    "sex": 1,
    "age": 30,
    "hospitalizationNo": "H001",
    "inhospitalDiagnose": "高血压",
    "deptId": 1,
    "sickbedNo": "1-101",
    "inhospitalTime": "2024-01-01T10:00:00",
    "status": 1,
    "category": "医保"
  },
  {
    "inpatientInfoId": "P002",
    "name": "李四",
    "idCard": "110101199002021234",
    "mobile": "13800138002",
    "sex": 2,
    "age": 25,
    "hospitalizationNo": "H002",
    "inhospitalDiagnose": "糖尿病",
    "deptId": 2,
    "sickbedNo": "2-102",
    "inhospitalTime": "2024-01-02T11:00:00",
    "status": 1,
    "category": "自费"
  },
  {
    "inpatientInfoId": "P003",
    "name": "王五",
    "idCard": "110101199003031234",
    "mobile": "13800138003",
    "sex": 1,
    "age": 35,
    "hospitalizationNo": "H003",
    "inhospitalDiagnose": "肺炎",
    "deptId": 1,
    "sickbedNo": "1-103",
    "inhospitalTime": "2024-01-03T12:00:00",
    "outhospitalTime": "2024-01-10T14:00:00",
    "status": 2,
    "category": "医保"
  },
  {
    "inpatientInfoId": "P004",
    "name": "赵六",
    "idCard": "110101199004041234",
    "mobile": "13800138004",
    "sex": 2,
    "age": 28,
    "hospitalizationNo": "H004",
    "inhospitalDiagnose": "阑尾炎",
    "deptId": 2,
    "sickbedNo": "2-104",
    "inhospitalTime": "2024-01-04T13:00:00",
    "outhospitalTime": "2024-01-08T16:00:00",
    "status": 2,
    "category": "自费"
  }
]'

echo "测试数据说明："
echo "- P001, P002: 在院患者 (status=1) -> 应该存储到 in 和 up 分类"
echo "- P003, P004: 出院患者 (status=2) -> 应该存储到 out 分类"
echo ""

echo "1. 测试患者数据全量同步（auto分类）"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/patient"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"

START_TIME=$(date +%s%3N)
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/patient" \
  -H "Content-Type: application/json" \
  -d "$PATIENT_DATA")
END_TIME=$(date +%s%3N)
DURATION=$((END_TIME - START_TIME))

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
echo "接口响应时间: ${DURATION}ms"
echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID并等待完成
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待5秒后查询任务状态..."
    sleep 5
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

echo "2. 验证Redis中的数据存储"
echo "预期的Redis键："
echo "- hospital:patient:type:$HOSPITAL_ID:in (应包含P001, P002)"
echo "- hospital:patient:type:$HOSPITAL_ID:up (应包含P001, P002)"
echo "- hospital:patient:type:$HOSPITAL_ID:out (应包含P003, P004)"
echo ""

echo "可以通过以下Redis命令验证："
echo "redis-cli HKEYS hospital:patient:type:$HOSPITAL_ID:in"
echo "redis-cli HKEYS hospital:patient:type:$HOSPITAL_ID:up"
echo "redis-cli HKEYS hospital:patient:type:$HOSPITAL_ID:out"
echo ""

echo "字段名格式应该是: hosptime_住院号_入院时间"
echo "例如: hosptime_H001_20240101100000"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "说明："
echo "1. 全量同步接口现在按照患者接口的auto方式分类处理患者数据"
echo "2. 在院患者(status=1)会同时存储到in和up分类中"
echo "3. 出院患者(status=2)会存储到out分类中"
echo "4. 使用的Redis键格式与患者接口完全一致"
echo "5. 字段名使用住院号+入院时间的格式"
