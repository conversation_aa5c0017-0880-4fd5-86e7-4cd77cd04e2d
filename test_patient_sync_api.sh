#!/bin/bash

# 患者分类同步接口测试脚本
# 使用方法: ./test_patient_sync_api.sh

BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="H001"

echo "=== 患者分类同步接口测试 ==="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo ""

# 测试1: 入院患者同步
echo "1. 测试入院患者同步"
echo "请求URL: $BASE_URL/api/sync/patient/$HOSPITAL_ID/in"

IN_PATIENT_DATA='[
  {
    "inpatientInfoId": "P001",
    "name": "张三",
    "idCard": "110101199001011234",
    "mobile": "13800138001",
    "sex": 1,
    "age": 30,
    "birthday": "1990-01-01",
    "hospitalizationNo": "H001",
    "inhospitalDiagnose": "高血压",
    "deptId": 1,
    "sickbedNo": "1-101",
    "doctorId": "D001",
    "nurseId": "N001",
    "nurseLevel": 2,
    "inhospitalTime": "2024-07-18T10:00:00",
    "status": 1,
    "category": "医保",
    "inpatientWard": "内科一病区",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "inpatientInfoId": "P002",
    "name": "李四",
    "idCard": "110101199002021234",
    "mobile": "13800138002",
    "sex": 2,
    "age": 25,
    "birthday": "1990-02-02",
    "hospitalizationNo": "H002",
    "inhospitalDiagnose": "糖尿病",
    "deptId": 1,
    "sickbedNo": "1-102",
    "doctorId": "D001",
    "nurseId": "N001",
    "nurseLevel": 2,
    "inhospitalTime": "2024-07-18T11:00:00",
    "status": 1,
    "category": "医保",
    "inpatientWard": "内科一病区",
    "updatedAt": "2024-07-18T11:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
START_TIME=$(date +%s%3N)

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/in" \
  -H "Content-Type: application/json" \
  -d "$IN_PATIENT_DATA")

END_TIME=$(date +%s%3N)
DURATION=$((END_TIME - START_TIME))

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
echo "接口响应时间: ${DURATION}ms"
echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待3秒后查询任务状态..."
    sleep 3
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

# 测试2: 在院患者同步
echo "2. 测试在院患者同步"
echo "请求URL: $BASE_URL/api/sync/patient/$HOSPITAL_ID/up"

UP_PATIENT_DATA='[
  {
    "inpatientInfoId": "P003",
    "name": "王五",
    "idCard": "110101199003031234",
    "mobile": "13800138003",
    "sex": 1,
    "age": 35,
    "birthday": "1990-03-03",
    "hospitalizationNo": "H003",
    "inhospitalDiagnose": "肺炎",
    "deptId": 2,
    "sickbedNo": "2-101",
    "doctorId": "D002",
    "nurseId": "N002",
    "nurseLevel": 3,
    "inhospitalTime": "2024-07-10T10:00:00",
    "status": 1,
    "category": "自费",
    "inpatientWard": "外科一病区",
    "updatedAt": "2024-07-18T12:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
START_TIME=$(date +%s%3N)

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/up" \
  -H "Content-Type: application/json" \
  -d "$UP_PATIENT_DATA")

END_TIME=$(date +%s%3N)
DURATION=$((END_TIME - START_TIME))

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
echo "接口响应时间: ${DURATION}ms"
echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待3秒后查询任务状态..."
    sleep 3
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

# 测试3: 出院患者同步
echo "3. 测试出院患者同步"
echo "请求URL: $BASE_URL/api/sync/patient/$HOSPITAL_ID/out"

OUT_PATIENT_DATA='[
  {
    "inpatientInfoId": "P004",
    "name": "赵六",
    "idCard": "110101199004041234",
    "mobile": "13800138004",
    "sex": 2,
    "age": 28,
    "birthday": "1990-04-04",
    "hospitalizationNo": "H004",
    "inhospitalDiagnose": "阑尾炎",
    "deptId": 2,
    "sickbedNo": "2-102",
    "doctorId": "D002",
    "nurseId": "N002",
    "nurseLevel": 2,
    "inhospitalTime": "2024-07-01T10:00:00",
    "outhospitalTime": "2024-07-15T14:00:00",
    "status": 2,
    "category": "自费",
    "inpatientWard": "外科一病区",
    "updatedAt": "2024-07-15T14:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
START_TIME=$(date +%s%3N)

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/out" \
  -H "Content-Type: application/json" \
  -d "$OUT_PATIENT_DATA")

END_TIME=$(date +%s%3N)
DURATION=$((END_TIME - START_TIME))

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
echo "接口响应时间: ${DURATION}ms"
echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待3秒后查询任务状态..."
    sleep 3
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

# 测试4: 自动分类患者同步
echo "4. 测试自动分类患者同步"
echo "请求URL: $BASE_URL/api/sync/patient/$HOSPITAL_ID/auto"

AUTO_PATIENT_DATA='[
  {
    "inpatientInfoId": "P005",
    "name": "孙七",
    "idCard": "110101199005051234",
    "mobile": "13800138005",
    "sex": 1,
    "age": 32,
    "birthday": "1990-05-05",
    "hospitalizationNo": "H005",
    "inhospitalDiagnose": "胃炎",
    "deptId": 1,
    "sickbedNo": "1-103",
    "doctorId": "D001",
    "nurseId": "N001",
    "nurseLevel": 2,
    "inhospitalTime": "2024-07-18T13:00:00",
    "status": 1,
    "category": "医保",
    "inpatientWard": "内科一病区",
    "updatedAt": "2024-07-18T13:00:00"
  },
  {
    "inpatientInfoId": "P006",
    "name": "周八",
    "idCard": "110101199006061234",
    "mobile": "13800138006",
    "sex": 2,
    "age": 29,
    "birthday": "1990-06-06",
    "hospitalizationNo": "H006",
    "inhospitalDiagnose": "骨折",
    "deptId": 2,
    "sickbedNo": "2-103",
    "doctorId": "D002",
    "nurseId": "N002",
    "nurseLevel": 3,
    "inhospitalTime": "2024-07-05T10:00:00",
    "outhospitalTime": "2024-07-17T16:00:00",
    "status": 2,
    "category": "医保",
    "inpatientWard": "外科一病区",
    "updatedAt": "2024-07-17T16:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
START_TIME=$(date +%s%3N)

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/auto" \
  -H "Content-Type: application/json" \
  -d "$AUTO_PATIENT_DATA")

END_TIME=$(date +%s%3N)
DURATION=$((END_TIME - START_TIME))

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
echo "接口响应时间: ${DURATION}ms"
echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待3秒后查询任务状态..."
    sleep 3
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "=== 测试完成 ==="
echo ""
echo "说明："
echo "1. 入院患者(in): 新入院的患者，status通常为1"
echo "2. 在院患者(up): 正在住院的患者，status为1"
echo "3. 出院患者(out): 已出院的患者，status为2，有出院时间"
echo "4. 自动分类(auto): 根据患者状态自动分类"
echo "   - status=1的患者会同时存储到in和up分类"
echo "   - status=2的患者会存储到out分类"
echo "5. Redis存储键格式: hospital:patient:type:{hospitalId}:{patientType}"
echo "6. 字段名格式: hosptime_{住院号}_{入院时间yyyyMMddHHmmss}"
