#!/bin/bash

# 测试任务状态接口的详细统计信息
BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="223"

echo "=== 测试任务状态接口详细统计信息 ==="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo ""

# 准备测试数据（包含有效和无效数据）
TEST_DATA='[
  {
    "inpatientInfoId": "P001",
    "name": "张三",
    "idCard": "110101199001011234",
    "mobile": "13800138001",
    "sex": 1,
    "age": 30,
    "hospitalizationNo": "H001",
    "inhospitalDiagnose": "高血压",
    "deptId": 1,
    "sickbedNo": "1-101",
    "inhospitalTime": "2024-01-01T10:00:00",
    "status": 1,
    "category": "医保"
  },
  {
    "inpatientInfoId": "P002",
    "name": "李四",
    "idCard": "110101199002021234",
    "mobile": "13800138002",
    "sex": 2,
    "age": 25,
    "hospitalizationNo": "H002",
    "inhospitalDiagnose": "糖尿病",
    "deptId": 2,
    "sickbedNo": "2-102",
    "inhospitalTime": "2024-01-02T11:00:00",
    "status": 2,
    "category": "自费"
  },
  {
    "inpatientInfoId": "P003",
    "name": "王五",
    "mobile": "13800138003",
    "sex": 1,
    "age": 35,
    "inhospitalDiagnose": "肺炎",
    "deptId": 1,
    "sickbedNo": "1-103",
    "status": 1,
    "category": "医保"
  }
]'

echo "测试数据说明："
echo "- P001: 完整有效数据，在院患者 (status=1)"
echo "- P002: 完整有效数据，出院患者 (status=2)"
echo "- P003: 无效数据（缺少住院号和入院时间）"
echo ""

echo "1. 发起全量同步任务"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/patient"

START_TIME=$(date +%s%3N)
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/patient" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA")
END_TIME=$(date +%s%3N)
DURATION=$((END_TIME - START_TIME))

echo "接口响应时间: ${DURATION}ms"
echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo ""
    
    echo "2. 等待任务完成并查询详细状态"
    echo "等待5秒后查询任务状态..."
    sleep 5
    
    echo "查询任务状态:"
    echo "请求URL: $BASE_URL/api/sync/task/$TASK_ID"
    
    TASK_RESPONSE=$(curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID")
    echo "$TASK_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$TASK_RESPONSE"
    echo ""
    
    echo "3. 解析统计信息"
    echo "从响应中提取关键统计信息："
    
    # 提取统计信息
    TOTAL_COUNT=$(echo "$TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('totalCount', 'N/A'))" 2>/dev/null)
    SUCCESS_COUNT=$(echo "$TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('successCount', 'N/A'))" 2>/dev/null)
    UPDATED_COUNT=$(echo "$TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('updatedCount', 'N/A'))" 2>/dev/null)
    INSERTED_COUNT=$(echo "$TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('insertedCount', 'N/A'))" 2>/dev/null)
    SKIPPED_COUNT=$(echo "$TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('skippedCount', 'N/A'))" 2>/dev/null)
    FAILED_COUNT=$(echo "$TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('failedCount', 'N/A'))" 2>/dev/null)
    
    echo "- 总处理数据量: $TOTAL_COUNT"
    echo "- 成功处理数量: $SUCCESS_COUNT"
    echo "- 更新数据量: $UPDATED_COUNT"
    echo "- 插入数据量: $INSERTED_COUNT"
    echo "- 跳过数据量: $SKIPPED_COUNT"
    echo "- 失败数据量: $FAILED_COUNT"
    echo ""
    
    # 提取分类统计信息
    echo "4. 分类统计信息（患者auto分类）:"
    CLASSIFICATION_STATS=$(echo "$TASK_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    stats = data.get('data', {}).get('classificationStats', {})
    if stats:
        for key, value in stats.items():
            print(f'- {key}: {value}')
    else:
        print('- 无分类统计信息')
except:
    print('- 解析失败')
" 2>/dev/null)
    echo "$CLASSIFICATION_STATS"
    echo ""
    
    # 提取性能统计信息
    echo "5. 性能统计信息:"
    PERFORMANCE_STATS=$(echo "$TASK_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    stats = data.get('data', {}).get('performanceStats', {})
    if stats:
        for key, value in stats.items():
            print(f'- {key}: {value}')
    else:
        print('- 无性能统计信息')
except:
    print('- 解析失败')
" 2>/dev/null)
    echo "$PERFORMANCE_STATS"
    echo ""
    
else
    echo "❌ 未能获取任务ID，无法查询任务状态"
fi

echo "----------------------------------------"

echo "6. 测试科室数据全量同步（对比）"
DEPT_DATA='[
  {
    "deptId": 1,
    "deptName": "内科",
    "deptCode": "NK",
    "parentId": 0,
    "deptLevel": 1,
    "status": 1
  },
  {
    "deptId": 2,
    "deptName": "外科",
    "deptCode": "WK",
    "parentId": 0,
    "deptLevel": 1,
    "status": 1
  }
]'

echo "发起科室数据全量同步..."
DEPT_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/department" \
  -H "Content-Type: application/json" \
  -d "$DEPT_DATA")

DEPT_TASK_ID=$(echo "$DEPT_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$DEPT_TASK_ID" ]; then
    echo "科室同步任务ID: $DEPT_TASK_ID"
    sleep 3
    
    echo "查询科室同步任务状态:"
    DEPT_TASK_RESPONSE=$(curl -s -X GET "$BASE_URL/api/sync/task/$DEPT_TASK_ID")
    echo "$DEPT_TASK_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$DEPT_TASK_RESPONSE"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "预期结果："
echo "1. 任务状态接口应该返回详细的统计信息"
echo "2. 包含totalCount, successCount, updatedCount, insertedCount, skippedCount, failedCount"
echo "3. 患者数据应该包含classificationStats（in, up, out分类统计）"
echo "4. 应该包含performanceStats（性能统计信息）"
echo "5. 科室数据不应该有classificationStats，但应该有其他统计信息"
