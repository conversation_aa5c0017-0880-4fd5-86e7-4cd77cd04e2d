#!/bin/bash

# 综合同步接口测试脚本
# 测试所有同步相关的API接口
# 使用方法: ./test_all_sync_apis.sh

BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="H001"

echo "=========================================="
echo "=== SysGetway 综合同步接口测试 ==="
echo "=========================================="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 函数：等待并查询任务状态
wait_and_check_task() {
    local response="$1"
    local task_name="$2"
    
    TASK_ID=$(echo "$response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)
    
    if [ ! -z "$TASK_ID" ]; then
        echo "任务ID: $TASK_ID"
        echo "等待3秒后查询任务状态..."
        sleep 3
        
        echo "[$task_name] 任务状态查询:"
        curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
        echo ""
    fi
}

# 测试1: 科室数据全量同步
echo "===========================================" 
echo "测试1: 科室数据全量同步"
echo "==========================================="
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/department"

DEPT_DATA='[
  {
    "departmentId": 1,
    "code": "NK",
    "name": "内科",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "departmentId": 2,
    "code": "WK",
    "name": "外科",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "departmentId": 3,
    "code": "EK",
    "name": "儿科",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "departmentId": 4,
    "code": "FCK",
    "name": "妇产科",
    "updatedAt": "2024-07-18T10:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/department" \
  -H "Content-Type: application/json" \
  -d "$DEPT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

wait_and_check_task "$RESPONSE" "科室数据全量同步"

echo "----------------------------------------"

# 测试2: 医护人员数据全量同步
echo "测试2: 医护人员数据全量同步"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/user"

USER_DATA='[
  {
    "userName": "D001",
    "name": "张医生",
    "sex": 1,
    "roleId": 1,
    "deptId": 1,
    "mobile": "13800138001",
    "inpatientWard": "内科一病区",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "userName": "N001",
    "name": "李护士",
    "sex": 2,
    "roleId": 2,
    "deptId": 1,
    "mobile": "13800138002",
    "inpatientWard": "内科一病区,内科二病区",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "userName": "D002",
    "name": "王医生",
    "sex": 1,
    "roleId": 1,
    "deptId": 2,
    "mobile": "13800138003",
    "inpatientWard": "外科一病区",
    "updatedAt": "2024-07-18T10:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/user" \
  -H "Content-Type: application/json" \
  -d "$USER_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

wait_and_check_task "$RESPONSE" "医护人员数据全量同步"

echo "----------------------------------------"

# 测试3: 患者数据全量同步（自动分类）
echo "测试3: 患者数据全量同步（自动分类）"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/patient"

PATIENT_DATA='[
  {
    "inpatientInfoId": "P001",
    "name": "张三",
    "idCard": "110101199001011234",
    "mobile": "13800138001",
    "sex": 1,
    "age": 30,
    "birthday": "1990-01-01",
    "hospitalizationNo": "H001",
    "inhospitalDiagnose": "高血压",
    "deptId": 1,
    "sickbedNo": "1-101",
    "doctorId": "D001",
    "nurseId": "N001",
    "nurseLevel": 2,
    "inhospitalTime": "2024-07-18T10:00:00",
    "status": 1,
    "category": "医保",
    "inpatientWard": "内科一病区",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "inpatientInfoId": "P002",
    "name": "李四",
    "idCard": "110101199002021234",
    "mobile": "13800138002",
    "sex": 2,
    "age": 25,
    "birthday": "1990-02-02",
    "hospitalizationNo": "H002",
    "inhospitalDiagnose": "糖尿病",
    "deptId": 1,
    "sickbedNo": "1-102",
    "doctorId": "D001",
    "nurseId": "N001",
    "nurseLevel": 2,
    "inhospitalTime": "2024-07-18T11:00:00",
    "status": 1,
    "category": "医保",
    "inpatientWard": "内科一病区",
    "updatedAt": "2024-07-18T11:00:00"
  },
  {
    "inpatientInfoId": "P003",
    "name": "王五",
    "idCard": "110101199003031234",
    "mobile": "13800138003",
    "sex": 1,
    "age": 35,
    "birthday": "1990-03-03",
    "hospitalizationNo": "H003",
    "inhospitalDiagnose": "肺炎",
    "deptId": 2,
    "sickbedNo": "2-101",
    "doctorId": "D002",
    "nurseId": "N002",
    "nurseLevel": 3,
    "inhospitalTime": "2024-07-01T10:00:00",
    "outhospitalTime": "2024-07-15T14:00:00",
    "status": 2,
    "category": "自费",
    "inpatientWard": "外科一病区",
    "updatedAt": "2024-07-15T14:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/patient" \
  -H "Content-Type: application/json" \
  -d "$PATIENT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

wait_and_check_task "$RESPONSE" "患者数据全量同步"

echo "----------------------------------------"

# 测试4: 患者分类同步 - 入院患者
echo "测试4: 患者分类同步 - 入院患者"
echo "请求URL: $BASE_URL/api/sync/patient/$HOSPITAL_ID/in"

IN_PATIENT_DATA='[
  {
    "inpatientInfoId": "P004",
    "name": "赵六",
    "idCard": "110101199004041234",
    "mobile": "13800138004",
    "sex": 2,
    "age": 28,
    "birthday": "1990-04-04",
    "hospitalizationNo": "H004",
    "inhospitalDiagnose": "阑尾炎",
    "deptId": 2,
    "sickbedNo": "2-102",
    "doctorId": "D002",
    "nurseId": "N002",
    "nurseLevel": 2,
    "inhospitalTime": "2024-07-18T14:00:00",
    "status": 1,
    "category": "自费",
    "inpatientWard": "外科一病区",
    "updatedAt": "2024-07-18T14:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/in" \
  -H "Content-Type: application/json" \
  -d "$IN_PATIENT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

wait_and_check_task "$RESPONSE" "入院患者同步"

echo "----------------------------------------"

# 测试5: 患者分类同步 - 出院患者
echo "测试5: 患者分类同步 - 出院患者"
echo "请求URL: $BASE_URL/api/sync/patient/$HOSPITAL_ID/out"

OUT_PATIENT_DATA='[
  {
    "inpatientInfoId": "P005",
    "name": "孙七",
    "idCard": "110101199005051234",
    "mobile": "13800138005",
    "sex": 1,
    "age": 32,
    "birthday": "1990-05-05",
    "hospitalizationNo": "H005",
    "inhospitalDiagnose": "胃炎",
    "deptId": 1,
    "sickbedNo": "1-103",
    "doctorId": "D001",
    "nurseId": "N001",
    "nurseLevel": 2,
    "inhospitalTime": "2024-07-10T10:00:00",
    "outhospitalTime": "2024-07-18T15:00:00",
    "status": 2,
    "category": "医保",
    "inpatientWard": "内科一病区",
    "updatedAt": "2024-07-18T15:00:00"
  }
]'

echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')"
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/out" \
  -H "Content-Type: application/json" \
  -d "$OUT_PATIENT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

wait_and_check_task "$RESPONSE" "出院患者同步"

echo "=========================================="
echo "=== 状态查询测试 ==="
echo "=========================================="

# 测试6: 查询同步状态
echo "测试6: 查询各类数据同步状态"

for table_name in "department" "user" "patient" "patient:in" "patient:up" "patient:out"; do
    echo "查询 $table_name 同步状态:"
    curl -s -X GET "$BASE_URL/api/sync/status/$HOSPITAL_ID/$table_name" | python3 -m json.tool 2>/dev/null
    echo ""
done

echo "=========================================="
echo "=== 测试完成 ==="
echo "=========================================="
echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo "测试总结："
echo "1. ✅ 科室数据全量同步"
echo "2. ✅ 医护人员数据全量同步"
echo "3. ✅ 患者数据全量同步（自动分类）"
echo "4. ✅ 患者分类同步 - 入院患者"
echo "5. ✅ 患者分类同步 - 出院患者"
echo "6. ✅ 同步状态查询"
echo ""
echo "注意事项："
echo "- 所有同步操作都是异步执行"
echo "- 可通过返回的taskId查询详细状态"
echo "- 全量同步会清空原有数据"
echo "- 患者分类同步是增量更新"
echo "- Redis键格式: hospital:{type}:{hospitalId} 或 hospital:patient:type:{hospitalId}:{patientType}"
