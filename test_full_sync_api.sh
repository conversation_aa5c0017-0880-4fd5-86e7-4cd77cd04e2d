#!/bin/bash

# 全量同步接口测试脚本
# 使用方法: ./test_full_sync_api.sh

BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="H001"

echo "=== 全量同步接口测试 ==="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo ""

# 测试1: 科室数据全量同步（提供数据）
echo "1. 测试科室数据全量同步（提供数据）"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/department"

DEPT_DATA='[
  {
    "deptId": 1,
    "deptName": "内科",
    "deptCode": "NK",
    "parentId": 0,
    "deptLevel": 1,
    "status": 1,
    "remark": "内科科室"
  },
  {
    "deptId": 2,
    "deptName": "外科",
    "deptCode": "WK", 
    "parentId": 0,
    "deptLevel": 1,
    "status": 1,
    "remark": "外科科室"
  },
  {
    "deptId": 3,
    "deptName": "儿科",
    "deptCode": "EK",
    "parentId": 0,
    "deptLevel": 1,
    "status": 1,
    "remark": "儿科科室"
  }
]'

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/department" \
  -H "Content-Type: application/json" \
  -d "$DEPT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待3秒后查询任务状态..."
    sleep 3
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

# 测试2: 用户数据全量同步（提供数据）
echo "2. 测试用户数据全量同步（提供数据）"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/user"

USER_DATA='[
  {
    "userId": "U001",
    "userName": "张医生",
    "userCode": "zhangys",
    "deptId": 1,
    "position": "主治医师",
    "mobile": "13800138001",
    "email": "<EMAIL>",
    "status": 1
  },
  {
    "userId": "U002", 
    "userName": "李护士",
    "userCode": "lihs",
    "deptId": 2,
    "position": "护士长",
    "mobile": "13800138002",
    "email": "<EMAIL>",
    "status": 1
  }
]'

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/user" \
  -H "Content-Type: application/json" \
  -d "$USER_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待3秒后查询任务状态..."
    sleep 3
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

# 测试3: 患者数据全量同步（不提供数据，从外部系统获取）
echo "3. 测试患者数据全量同步（不提供数据）"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/patient"

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/patient" \
  -H "Content-Type: application/json")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待5秒后查询任务状态（患者数据获取可能需要更长时间）..."
    sleep 5
    
    echo "查询任务状态:"
    curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID" | python3 -m json.tool 2>/dev/null
    echo ""
fi

echo "----------------------------------------"

# 测试4: 错误情况测试 - 无效表名
echo "4. 测试错误情况 - 无效表名"
echo "请求URL: $BASE_URL/api/sync/full/$HOSPITAL_ID/invalid_table"

RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/invalid_table" \
  -H "Content-Type: application/json" \
  -d '[]')

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "说明："
echo "1. 如果服务未启动，请先启动应用"
echo "2. 确保Redis服务正在运行"
echo "3. 可以通过任务ID查询具体的处理状态和结果"
echo "4. 数据会完全替换Redis中的原有数据"
