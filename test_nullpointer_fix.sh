#!/bin/bash

# 测试NullPointerException修复
BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="8"

echo "=== 测试NullPointerException修复 ==="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo ""

# 测试1: 患者数据同步（之前出错的场景）
echo "1. 测试患者数据同步（auto类型）"
PATIENT_DATA='[
  {
    "inpatientInfoId": "P001",
    "name": "张三",
    "idCard": "110101199001011234",
    "mobile": "13800138001",
    "sex": 1,
    "age": 30,
    "hospitalizationNo": "H001",
    "inhospitalDiagnose": "高血压",
    "deptId": 1,
    "sickbedNo": "1-101",
    "inhospitalTime": "2024-01-01T10:00:00",
    "status": 1,
    "category": "医保"
  },
  {
    "inpatientInfoId": "P002",
    "name": "李四",
    "idCard": "110101199002021234",
    "mobile": "13800138002",
    "sex": 2,
    "age": 25,
    "hospitalizationNo": "H002",
    "inhospitalDiagnose": "糖尿病",
    "deptId": 2,
    "sickbedNo": "2-102",
    "inhospitalTime": "2024-01-02T11:00:00",
    "status": 2,
    "category": "自费"
  }
]'

echo "发起患者数据同步..."
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/auto" \
  -H "Content-Type: application/json" \
  -d "$PATIENT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

# 提取任务ID
TASK_ID=$(echo "$RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$TASK_ID" ]; then
    echo "任务ID: $TASK_ID"
    echo "等待5秒后查询任务状态..."
    sleep 5
    
    echo "查询任务状态:"
    TASK_RESPONSE=$(curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID")
    echo "$TASK_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$TASK_RESPONSE"
    echo ""
    
    # 检查是否还有NullPointerException
    STATUS=$(echo "$TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('status', 'N/A'))" 2>/dev/null)
    TOTAL_COUNT=$(echo "$TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('totalCount', 'N/A'))" 2>/dev/null)
    SUCCESS_COUNT=$(echo "$TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('successCount', 'N/A'))" 2>/dev/null)
    
    echo "检查结果:"
    echo "- 任务状态: $STATUS"
    echo "- 总数据量: $TOTAL_COUNT"
    echo "- 成功数量: $SUCCESS_COUNT"
    
    if [ "$STATUS" = "-1" ]; then
        echo "❌ 任务仍然失败"
    elif [ "$TOTAL_COUNT" = "null" ] || [ "$TOTAL_COUNT" = "N/A" ]; then
        echo "❌ 统计信息仍然为null"
    else
        echo "✅ 任务执行成功，统计信息正常"
    fi
else
    echo "❌ 未能获取任务ID"
fi

echo "----------------------------------------"

# 测试2: 全量同步患者数据
echo "2. 测试全量同步患者数据"
echo "发起全量同步..."
FULL_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/patient" \
  -H "Content-Type: application/json" \
  -d "$PATIENT_DATA")

echo "响应结果:"
echo "$FULL_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$FULL_RESPONSE"
echo ""

FULL_TASK_ID=$(echo "$FULL_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$FULL_TASK_ID" ]; then
    echo "全量同步任务ID: $FULL_TASK_ID"
    echo "等待5秒后查询任务状态..."
    sleep 5
    
    echo "查询全量同步任务状态:"
    FULL_TASK_RESPONSE=$(curl -s -X GET "$BASE_URL/api/sync/task/$FULL_TASK_ID")
    echo "$FULL_TASK_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$FULL_TASK_RESPONSE"
    echo ""
    
    # 检查全量同步结果
    FULL_STATUS=$(echo "$FULL_TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('status', 'N/A'))" 2>/dev/null)
    CLASSIFICATION_STATS=$(echo "$FULL_TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print('有' if data.get('data', {}).get('classificationStats') else '无')" 2>/dev/null)
    
    echo "全量同步检查结果:"
    echo "- 任务状态: $FULL_STATUS"
    echo "- 分类统计: $CLASSIFICATION_STATS"
    
    if [ "$FULL_STATUS" = "1" ] && [ "$CLASSIFICATION_STATS" = "有" ]; then
        echo "✅ 全量同步执行成功，包含分类统计"
    else
        echo "❌ 全量同步存在问题"
    fi
else
    echo "❌ 未能获取全量同步任务ID"
fi

echo "----------------------------------------"

# 测试3: 空数据测试
echo "3. 测试空数据处理"
echo "发起空数据同步..."
EMPTY_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/auto" \
  -H "Content-Type: application/json" \
  -d '[]')

echo "空数据响应结果:"
echo "$EMPTY_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$EMPTY_RESPONSE"
echo ""

EMPTY_TASK_ID=$(echo "$EMPTY_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)

if [ ! -z "$EMPTY_TASK_ID" ]; then
    echo "空数据任务ID: $EMPTY_TASK_ID"
    sleep 3
    
    echo "查询空数据任务状态:"
    EMPTY_TASK_RESPONSE=$(curl -s -X GET "$BASE_URL/api/sync/task/$EMPTY_TASK_ID")
    echo "$EMPTY_TASK_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$EMPTY_TASK_RESPONSE"
    
    EMPTY_STATUS=$(echo "$EMPTY_TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('status', 'N/A'))" 2>/dev/null)
    EMPTY_TOTAL=$(echo "$EMPTY_TASK_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('totalCount', 'N/A'))" 2>/dev/null)
    
    echo "空数据检查结果:"
    echo "- 任务状态: $EMPTY_STATUS"
    echo "- 总数据量: $EMPTY_TOTAL"
    
    if [ "$EMPTY_STATUS" = "1" ] && [ "$EMPTY_TOTAL" = "0" ]; then
        echo "✅ 空数据处理正常"
    else
        echo "❌ 空数据处理存在问题"
    fi
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "修复内容："
echo "1. 修复了性能统计计算时的NullPointerException"
echo "2. 确保在计算性能统计前先设置costTime"
echo "3. 为空数据情况添加了完整的统计信息"
echo "4. 统一了所有同步任务的统计信息设置逻辑"
