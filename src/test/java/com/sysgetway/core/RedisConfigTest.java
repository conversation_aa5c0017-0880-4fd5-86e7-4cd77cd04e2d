package com.sysgetway.core;

import com.sysgetway.core.common.config.RedisConfig;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Redis配置测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class RedisConfigTest {

    @Test
    public void testRedisConfigCompilation() {
        // 测试Redis配置类是否可以正常编译和实例化
        RedisConfig redisConfig = new RedisConfig();
        assertNotNull(redisConfig);
        
        // 测试相关类是否可以正常导入和使用
        RedisConnectionFactory factory = null; // 在实际测试中会由Spring注入
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        Jackson2JsonRedisSerializer<Object> jsonSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        
        assertNotNull(template);
        assertNotNull(jsonSerializer);
        assertNotNull(stringSerializer);
        
        System.out.println("Redis配置类编译和导入测试通过！");
    }
}
