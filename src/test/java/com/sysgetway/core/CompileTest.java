package com.sysgetway.core;

import com.sysgetway.core.entity.Department;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.entity.User;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 编译测试类，验证实体类是否正确
 */
public class CompileTest {

    @Test
    public void testEntityCompilation() {
        // 测试Department实体
        Department department = new Department();
        department.setDepartmentId(1);
        department.setCode("DEPT001");
        department.setName("内科");
        department.setUpdatedAt(LocalDateTime.now());
        
        // 测试User实体
        User user = new User();
        user.setUserName("doctor001");
        user.setName("张医生");
        user.setDeptId(1);
        user.setPosition("医生");
        user.setPhone("13800138001");
        user.setMobile("13800138001");
        user.setSex(1);
        user.setRoleId(1);
        user.setInpatientWard("1病区");
        user.setUpdatedAt(LocalDateTime.now());
        
        // 测试Patient实体
        Patient patient = new Patient();
        patient.setInpatientInfoId("P001");
        patient.setName("张三");
        patient.setIdCard("110101199001011234");
        patient.setMobile("13800138006");
        patient.setSex(1);
        patient.setAge(30);
        patient.setBirthday(LocalDate.of(1990, 1, 1));
        patient.setHospitalizationNo("H001");
        patient.setInhospitalDiagnose("高血压");
        patient.setDeptId(2);
        patient.setSickbedNo("2-101");
        patient.setDoctorId("D002");
        patient.setNurseId("N002");
        patient.setNurseLevel(2);
        patient.setInhospitalTime(LocalDateTime.now());
        patient.setOuthospitalTime(null);
        patient.setStatus(1);
        patient.setCategory("医保");
        patient.setInpatientWard("2病区");
        patient.setUpdatedAt(LocalDateTime.now());
        
        System.out.println("所有实体类编译成功！");
        System.out.println("Department: " + department.getName());
        System.out.println("User: " + user.getName());
        System.out.println("Patient: " + patient.getName());
    }
}
