package com.sysgetway.core;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 应用启动测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class ApplicationStartupTest {

    @Test
    public void contextLoads() {
        // 这个测试验证Spring Boot应用上下文可以正常加载
        System.out.println("Spring Boot应用启动成功！");
    }
}
