package com.sysgetway.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 科室实体类（简化版，不包含数据库注解）
 */
@Data
@ApiModel(description = "科室信息实体")
public class Department implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 科室ID
     */
    @ApiModelProperty(value = "科室ID", required = true, example = "1")
    private Integer departmentId;

    /**
     * 科室编码
     */
    @ApiModelProperty(value = "科室编码", required = true, example = "DEPT001")
    private String code;

    /**
     * 科室名称
     */
    @ApiModelProperty(value = "科室名称", required = true, example = "内科")
    private String name;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间", example = "2023-01-01T10:00:00")
    private LocalDateTime updatedAt;
}
