package com.sysgetway.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 医护人员实体类（简化版，不包含数据库注解）
 */
@Data
@ApiModel(description = "医护人员信息实体")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", required = true, example = "doctor001")
    private String userName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true, example = "张医生")
    private String name;

    /**
     * 科室ID
     */
    @ApiModelProperty(value = "科室ID", example = "1")
    private Integer deptId;

    /**
     * 职位类型
     */
    @ApiModelProperty(value = "职位类型", example = "医生")
    private String position;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", example = "13800138001")
    private String phone;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", example = "13800138001")
    private String mobile;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", example = "1")
    private Integer sex;

    /**
     * 角色ID
     */
    @ApiModelProperty(value = "角色ID", example = "1")
    private Integer roleId;

    /**
     * 病区
     */
    @ApiModelProperty(value = "病区", example = "1病区")
    private String inpatientWard;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间", example = "2023-01-01T10:00:00")
    private LocalDateTime updatedAt;
}
