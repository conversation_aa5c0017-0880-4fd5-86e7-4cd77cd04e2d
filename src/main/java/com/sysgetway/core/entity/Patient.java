package com.sysgetway.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 患者实体类（简化版，不包含数据库注解）
 */
@Data
@ApiModel(description = "患者信息实体")
public class Patient implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 住院信息唯一ID
     */
    @ApiModelProperty(value = "住院信息唯一ID", required = true, example = "P001")
    private String inpatientInfoId;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名", required = true, example = "张三")
    private String name;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号", example = "110101199001011234")
    private String idCard;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", example = "13800138006")
    private String mobile;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", example = "1")
    private Integer sex;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄", example = "30")
    private Integer age;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日", example = "1990-01-01")
    private LocalDate birthday;

    /**
     * 住院号
     */
    @ApiModelProperty(value = "住院号", required = true, example = "H001")
    private String hospitalizationNo;

    /**
     * 入院诊断
     */
    @ApiModelProperty(value = "入院诊断", example = "高血压")
    private String inhospitalDiagnose;

    /**
     * 科室ID
     */
    @ApiModelProperty(value = "科室ID", example = "2")
    private Integer deptId;

    /**
     * 床位号
     */
    @ApiModelProperty(value = "床位号", example = "2-101")
    private String sickbedNo;

    /**
     * 责任医生ID
     */
    @ApiModelProperty(value = "责任医生ID", example = "D002")
    private String doctorId;

    /**
     * 责任护士ID
     */
    @ApiModelProperty(value = "责任护士ID", example = "N002")
    private String nurseId;

    /**
     * 护理级别
     */
    @ApiModelProperty(value = "护理级别", example = "2")
    private Integer nurseLevel;

    /**
     * 入院时间
     */
    @ApiModelProperty(value = "入院时间", required = true, example = "2023-06-10T10:00:00")
    private LocalDateTime inhospitalTime;

    /**
     * 出院时间
     */
    @ApiModelProperty(value = "出院时间", example = "2023-06-15T10:00:00")
    private LocalDateTime outhospitalTime;

    /**
     * 患者当前状态
     */
    @ApiModelProperty(value = "患者当前状态", example = "1")
    private Integer status;

    /**
     * 患者类别
     */
    @ApiModelProperty(value = "患者类别", example = "医保")
    private String category;

    /**
     * 病区
     */
    @ApiModelProperty(value = "病区", example = "2病区")
    private String inpatientWard;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间", example = "2023-06-10T10:00:00")
    private LocalDateTime updatedAt;
}
