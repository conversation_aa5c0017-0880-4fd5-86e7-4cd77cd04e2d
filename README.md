# SysGetway - 医疗数据同步网关系统

医疗数据同步网关（SysGetway）是一个基于 Spring Boot 的微服务，专注于实现HIS（医院信息系统）数据库与本地 Redis 之间的数据高效、安全、可靠传输。系统支持多医院数据同步，提供全量同步和增量同步功能，确保医疗数据的实时性和一致性。

## 核心功能

- **多医院支持**：通过医院ID（client_id）区分和隔离不同医院的数据
- **双模式同步**：支持全量同步和增量同步，满足不同场景需求
- **异步处理**：采用异步任务处理，提高系统并发能力和响应速度
- **任务管理**：完整的任务状态跟踪、重试机制和错误处理
- **数据分类**：智能患者数据分类（住院、门诊、急诊等）
- **性能监控**：实时监控同步性能、数据量统计和系统健康状态

## 技术栈

- **基础框架**：Spring Boot 2.7.14
- **数据访问**：MyBatis-Plus 3.5.3.1、Spring Data Redis
- **数据库**：MySQL 5.7+
- **缓存**：Redis 6.2+
- **API文档**：Knife4j 2.0.9 + OpenAPI 3.0
- **工具库**：Hutool 5.8.18、Guava 31.1、Apache Commons
- **代码生成**：MyBatis-Plus Generator
- **日志框架**：Logback
- **任务调度**：Spring @Async + CompletableFuture
- **JSON处理**：FastJSON2 2.0.33

## 项目结构

```
sysgetway/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── sysgetway/
│   │   │           └── core/
│   │   │               ├── SysGetwayApplication.java    # 启动类
│   │   │               ├── controller/                 # 控制器层
│   │   │               │   ├── SyncController.java     # 同步接口
│   │   │               │   ├── DataController.java     # 数据查询接口
│   │   │               │   ├── MonitorController.java  # 监控接口
│   │   │               │   └── DemoController.java     # 示例接口
│   │   │               ├── service/                    # 服务接口层
│   │   │               │   ├── SyncService.java        # 同步服务接口
│   │   │               │   └── impl/                   # 服务实现层
│   │   │               │       └── SyncServiceImpl.java # 同步服务实现
│   │   │               ├── entity/                     # 实体类
│   │   │               │   ├── Department.java         # 科室实体
│   │   │               │   ├── User.java               # 用户实体
│   │   │               │   └── Patient.java            # 患者实体
│   │   │               ├── model/                      # 数据传输对象
│   │   │               │   ├── dto/                    # DTO对象
│   │   │               │   └── vo/                     # VO对象
│   │   │               ├── common/                     # 通用组件
│   │   │               │   ├── config/                 # 配置类
│   │   │               │   ├── constant/               # 常量定义
│   │   │               │   ├── exception/              # 异常处理
│   │   │               │   └── util/                   # 工具类
│   │   │               └── generator/                  # 代码生成器
│   │   └── resources/
│   │       ├── application.yml                         # 主配置文件
│   │       ├── application-dev.yml                     # 开发环境配置
│   │       ├── application-test.yml                    # 测试环境配置
│   │       ├── application-prod.yml                    # 生产环境配置
│   │       ├── application-docker.yml                  # Docker环境配置
│   │       └── logback-spring.xml                      # 日志配置
│   └── test/                                           # 测试代码
├── Dockerfile                                          # Docker构建文件
├── docker-compose.yml                                  # Docker编排文件
├── build.sh                                            # 构建脚本
└── pom.xml                                             # Maven配置
```

## 核心API接口

### 同步接口 (SyncController) - `/api/sync`

#### 数据同步
- **全量同步**：`PUT /api/sync/full/{hospitalId}/{tableName}` - 清空并重新同步所有数据
  - 参数：hospitalId(医院ID), tableName(department/user/patient)
  - 支持可选的数据列表请求体
  - 科室数据：清空并重新存储所有科室信息
  - 医护人员数据：清空并重新存储所有医护人员信息
  - 患者数据：按照auto模式自动分类存储（在院患者存储到in和up，出院患者存储到out）

- **患者分类同步**：`PUT /api/sync/patient/{hospitalId}/{patientType}` - 患者分类同步
  - 参数：hospitalId(医院ID), patientType(in/up/out/auto)
  - in：入院患者同步（增量更新）
  - up：在院患者同步（增量更新）
  - out：出院患者同步（增量更新）
  - auto：自动分类患者同步（根据患者状态自动分类）


#### 状态查询
- **同步状态查询**：`GET /api/sync/status/{hospitalId}/{tableName}` - 查询同步状态
- **最后同步时间**：`GET /api/sync/last-time/{hospitalId}/{tableName}` - 获取最后同步时间
- **任务状态查询**：`GET /api/sync/task/{taskId}` - 通过任务ID查询异步任务状态

#### 任务管理
- **清理任务状态**：`DELETE /api/sync/task/{hospitalId}/{tableNameOrType}/{syncType}` - 清理指定任务状态
- **清理所有任务**：`DELETE /api/sync/task/all` - 清理所有任务状态

#### 测试接口
- **Redis测试**：`PUT /api/sync/test/redis/{hospitalId}/{patientType}` - 测试Redis患者数据存储

### 数据查询接口 (DataController) - `/api`

#### 患者数据
- **患者分类查询**：`GET /api/patients/{hospitalId}/{patientType}` - 查询分类患者数据
  - 参数：hospitalId(医院ID), patientType(in/up/out)
- **患者统计**：`GET /api/patients/{hospitalId}/stats` - 获取患者分类统计
- **患者数据导入**：`POST /api/patients/{hospitalId}/import` - 导入患者数据

#### 实体数据查询
- **条件查询**：`GET /api/data/{hospitalId}/{entityType}` - 条件查询实体数据
  - 参数：hospitalId(医院ID), entityType(department/user/patient)
  - 查询参数：keyword(关键字), limit(数量限制)
- **单个查询**：`GET /api/data/{hospitalId}/{entityType}/{entityId}` - 查询单个实体
- **批量查询**：`POST /api/data/{hospitalId}/{entityType}/batch` - 批量查询实体数据

### 监控接口 (MonitorController) - `/api/monitor`

#### 系统监控
- **系统健康检查**：`GET /api/monitor/health` - 系统健康状态
- **系统状态**：`GET /api/monitor/status` - 系统运行状态
- **内存使用情况**：`GET /api/monitor/memory` - 内存使用详情

#### 同步监控
- **患者同步统计**：`GET /api/monitor/patient-sync/stats/{hospitalId}` - 患者分类同步统计
- **同步任务统计**：`GET /api/monitor/sync/stats` - 同步任务统计
- **运行任务列表**：`GET /api/monitor/tasks` - 获取正在运行的任务



## 功能特性

### 数据同步特性
- **双模式同步**：支持全量同步和增量同步，满足不同业务场景
- **异步处理**：采用CompletableFuture实现异步任务处理，提高系统并发能力
- **任务管理**：完整的任务生命周期管理，包括状态跟踪、重试机制、错误处理
- **数据分类**：智能患者数据分类（入院、在院、出院），便于业务系统使用
- **多医院支持**：通过医院ID隔离不同医院数据，支持多租户场景

### 系统特性
- **统一响应格式**：所有API接口返回统一的JSON格式，包含状态码、消息、数据和时间戳
- **全局异常处理**：统一处理各类异常，包括业务异常、参数校验异常、系统异常等
- **API文档**：集成Knife4j + Swagger，提供美观、强大的API文档和在线调试功能
- **多环境配置**：支持开发、测试、生产、Docker等多环境配置分离
- **日志系统**：集成Logback，实现分级日志、文件滚动、异步日志等功能
- **性能监控**：实时监控同步性能、数据量统计和系统健康状态

## 快速开始

### 环境要求

- **JDK**：1.8 或更高版本
- **Maven**：3.6 或更高版本
- **MySQL**：5.7 或更高版本
- **Redis**：6.0 或更高版本
- **Docker**：20.10+ (可选，用于容器化部署)

### 本地开发

1. **克隆代码**：

```bash
git clone <your-repository-url>
cd sysgetway
```

2. **配置数据库**：

创建MySQL数据库：
```sql
CREATE DATABASE sysgetway DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **修改配置**：

编辑 `src/main/resources/application-dev.yml`，修改数据库和Redis连接信息：

```yaml
spring:
  datasource:
    url: ***************************************************************************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: your_redis_password  # 如果有密码
    database: 9
```

4. **编译运行**：

```bash
# 编译项目
mvn clean compile

# 运行项目（开发环境）
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/sysgetway-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev
```

5. **访问应用**：

- **API文档**：http://localhost:9000/sysgetway/doc.html
- **健康检查**：http://localhost:9000/sysgetway/api/monitor/health
- **示例接口**：http://localhost:9000/sysgetway/api/demo

## API使用示例

### 快速测试

项目提供了完整的测试脚本，可以快速验证API功能：

```bash
# 测试所有同步接口
./test_all_sync_apis.sh

# 测试医护人员同步接口
./test_user_sync_api.sh

# 测试患者分类同步接口
./test_patient_sync_api.sh

# 测试全量同步接口
./test_full_sync_api.sh
```

### 科室数据同步示例

```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/department" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "departmentId": 1,
      "code": "NK",
      "name": "内科",
      "updatedAt": "2024-07-18T10:00:00"
    },
    {
      "departmentId": 2,
      "code": "WK",
      "name": "外科",
      "updatedAt": "2024-07-18T10:00:00"
    }
  ]'
```

### 医护人员数据同步示例

```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/user" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "userName": "D001",
      "name": "张医生",
      "sex": 1,
      "roleId": 1,
      "deptId": 1,
      "mobile": "13800138001",
      "inpatientWard": "内科一病区"
    },
    {
      "userName": "N001",
      "name": "李护士",
      "sex": 2,
      "roleId": 2,
      "deptId": 1,
      "mobile": "13800138002",
      "inpatientWard": "内科一病区,内科二病区"
    }
  ]'
```

### 患者数据同步示例

```bash
# 患者全量同步（自动分类）
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/patient" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "inpatientInfoId": "P001",
      "name": "张三",
      "hospitalizationNo": "H001",
      "inhospitalTime": "2024-07-18T10:00:00",
      "status": 1,
      "deptId": 1
    }
  ]'

# 入院患者同步
curl -X PUT "http://localhost:9000/sysgetway/api/sync/patient/H001/in" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "inpatientInfoId": "P002",
      "name": "李四",
      "hospitalizationNo": "H002",
      "inhospitalTime": "2024-07-18T11:00:00",
      "status": 1,
      "deptId": 1
    }
  ]'
```

### 查询任务状态

```bash
# 通过任务ID查询状态
curl -X GET "http://localhost:9000/sysgetway/api/sync/task/{taskId}"

# 查询同步状态
curl -X GET "http://localhost:9000/sysgetway/api/sync/status/H001/department"
```

详细的API使用说明请参考：[API请求示例文档.md](./API请求示例文档.md)


## 多环境配置

系统支持多环境配置，通过Spring Profile机制实现：

- **开发环境**：`application-dev.yml` - 本地开发使用
- **测试环境**：`application-test.yml` - 测试环境使用
- **生产环境**：`application-prod.yml` - 生产环境使用
- **Docker环境**：`application-docker.yml` - Docker容器使用

### 环境切换

```bash
# 开发环境
java -jar sysgetway.jar --spring.profiles.active=dev

# 测试环境
java -jar sysgetway.jar --spring.profiles.active=test

# 生产环境
java -jar sysgetway.jar --spring.profiles.active=prod

# Docker环境
java -jar sysgetway.jar --spring.profiles.active=docker
```

## 项目部署

### Docker本地打包部署

#### 方法一：使用构建脚本（推荐）

执行以下命令，一键构建Docker镜像并保存为本地包：

```bash
# 添加执行权限
chmod +x build.sh

# 执行构建脚本
./build.sh
```

构建完成后，将在当前目录生成`sysgetway-1.0.0.tar`文件，可以将此文件拷贝到其他环境使用。

#### 方法二：手动构建

构建Docker镜像：

```bash
docker build -t sysgetway:1.0.0 .
```

保存为本地tar包：

```bash
docker save -o sysgetway-1.0.0.tar sysgetway:1.0.0
```

#### 在其他环境使用本地Docker镜像包运行容器

```bash
# 加载Docker镜像
docker load -i sysgetway-1.0.0.tar
# 把docker-compose.yml文件拷贝到目标环境
docker-compose.yml
# 添加执行权限
chmod +x docker-run.sh
# 执行构建脚本
./docker-run.sh


```




#### 使用docker-compose进行本地开发测试

项目提供了完整的docker-compose配置，包括应用、MySQL和Redis服务：

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看应用日志
docker-compose logs -f sysgetway

# 停止所有服务
docker-compose down
```

**服务访问地址**：
- **应用服务**：http://localhost:9000/sysgetway/doc.html
- **MySQL**：localhost:13306 (root/Aa123456)
- **Redis**：localhost:16379

## 监控和运维

### 系统监控

系统提供完整的监控接口：

```bash
# 系统健康检查
curl http://localhost:9000/sysgetway/api/monitor/health

# Redis连接状态
curl http://localhost:9000/sysgetway/api/monitor/redis

# 性能统计信息
curl http://localhost:9000/sysgetway/api/monitor/stats
```

### 日志管理

系统使用Logback进行日志管理，支持：

- **分级日志**：ERROR、WARN、INFO、DEBUG
- **文件滚动**：按日期和大小自动滚动
- **异步日志**：提高日志写入性能
- **日志分类**：
  - `logs/system.log` - 系统运行日志
  - `logs/business.log` - 业务操作日志
  - `logs/error.log` - 错误异常日志

### 性能调优

#### JVM参数优化

```bash
# 生产环境JVM参数建议
java -Xms1g -Xmx2g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError \
     -XX:HeapDumpPath=/app/logs/ \
     -jar sysgetway.jar --spring.profiles.active=prod
```

## 故障排查

### 常见问题及解决方案

1. **数据库连接失败**
   ```bash
   # 检查数据库服务状态
   systemctl status mysql

   # 测试数据库连接
   mysql -h localhost -u username -p
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis服务状态
   systemctl status redis

   # 测试Redis连接
   redis-cli -h localhost -p 6379 ping
   ```

3. **同步任务异常**
   ```bash
   # 查看业务日志
   tail -f logs/business.log | grep "sync"

   # 查看错误日志
   tail -f logs/error.log

   # 通过API查询任务状态
   curl http://localhost:9000/sysgetway/api/sync/task/status/{taskId}
   ```

### 日志分析命令

```bash
# 实时查看错误日志
tail -f logs/error.log

# 搜索特定关键词
grep -i "error\|exception" logs/system.log

# 统计同步任务数量
grep "sync" logs/business.log | wc -l

# 查看最近的同步记录
grep "sync" logs/business.log | tail -20
```

## 开发指南

### 项目结构说明

- **controller**：REST API控制器，处理HTTP请求
- **service**：业务逻辑层，实现核心业务功能
- **entity**：数据实体类，对应数据库表结构
- **model**：数据传输对象（DTO/VO），用于API数据交换
- **common**：通用组件，包括配置、常量、工具类等

### 添加新的同步表

1. **创建实体类**：
```java
@Data
@TableName("new_table")
public class NewEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String name;
    // 其他字段...
}
```

2. **扩展同步服务**：在 `SyncServiceImpl` 中添加对应的处理逻辑
3. **更新常量定义**：在 `SyncConstants` 中添加新表的常量
4. **编写测试用例**：确保新功能的正确性

### API开发规范

1. **统一返回格式**：使用 `ResponseResult<T>` 包装所有API响应
2. **参数校验**：使用 `@Valid` 和 `@Validated` 进行参数校验
3. **异常处理**：使用全局异常处理器统一处理异常
4. **API文档**：使用 `@ApiOperation` 等注解完善API文档

## 技术架构

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HIS数据库     │    │  SysGetway网关  │    │   Redis缓存     │
│                 │    │                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │科室数据   │  │◄──►│  │同步服务   │  │◄──►│  │科室缓存   │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │用户数据   │  │◄──►│  │数据转换   │  │◄──►│  │用户缓存   │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │患者数据   │  │◄──►│  │任务管理   │  │◄──►│  │患者缓存   │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据流程

1. **全量同步**：清空Redis缓存 → 从HIS数据库读取全量数据 → 数据转换 → 写入Redis
2. **增量同步**：获取最后同步时间 → 读取增量数据 → 数据转换 → 更新Redis
3. **任务管理**：异步任务执行 → 状态跟踪 → 结果通知 → 错误重试

## 贡献指南

1. Fork 项目
2. 创建特性分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交 Pull Request

